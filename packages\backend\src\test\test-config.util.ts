/**
 * Test Configuration Utility
 * 
 * Provides configuration for tests without hardcoded values.
 * All configuration is loaded from environment variables.
 */

export interface TestConfig {
  llm: {
    apiKey: string;
    baseUrl: string;
    model: string;
    temperature: number;
    maxTokens: number;
    timeout: number;
  };
  embedding: {
    apiKey: string;
    baseUrl: string;
    model: string;
    dimensions: number;
    batchSize: number;
  };
  pinecone: {
    apiKey: string;
    indexName: string;
    dimension: number;
  };
  skipExternalServices: boolean;
  mockExternalServices: boolean;
  testTimeout: number;
}

/**
 * Gets test configuration from environment variables
 * @returns Test configuration object
 * @throws Error if required test environment variables are missing
 */
export function getTestConfig(): TestConfig {
  // Check if we should skip external services
  const skipExternal = process.env.SKIP_EXTERNAL_SERVICES === 'true';
  const mockExternal = process.env.MOCK_EXTERNAL_SERVICES === 'true';

  if (skipExternal) {
    // Return minimal config for skipped tests
    return {
      llm: {
        apiKey: 'test-api-key',
        baseUrl: 'http://localhost:8000',
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000,
      },
      embedding: {
        apiKey: 'test-embedding-key',
        baseUrl: 'http://localhost:8001',
        model: 'test-embedding-model',
        dimensions: 384,
        batchSize: 100,
      },
      pinecone: {
        apiKey: 'test-pinecone-key',
        indexName: 'test-index',
        dimension: 384,
      },
      skipExternalServices: true,
      mockExternalServices: mockExternal,
      testTimeout: parseInt(process.env.TEST_TIMEOUT || '30000', 10),
    };
  }

  // For integration tests, require real configuration
  const requiredVars = [
    'LLM_API_KEY',
    'LLM_BASE_URL',
    'LLM_MODEL',
    'EMBEDDING_API_KEY',
    'EMBEDDING_BASE_URL',
    'EMBEDDING_MODEL',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required test environment variables:\n` +
      missingVars.map(v => `  - ${v}`).join('\n') + '\n\n' +
      'Please set these variables in your .env.local file or set SKIP_EXTERNAL_SERVICES=true to skip integration tests.'
    );
  }

  return {
    llm: {
      apiKey: process.env.LLM_API_KEY!,
      baseUrl: process.env.LLM_BASE_URL!,
      model: process.env.LLM_MODEL!,
      temperature: parseFloat(process.env.LLM_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.LLM_MAX_TOKENS || '2000', 10),
      timeout: parseInt(process.env.LLM_TIMEOUT || '60000', 10),
    },
    embedding: {
      apiKey: process.env.EMBEDDING_API_KEY!,
      baseUrl: process.env.EMBEDDING_BASE_URL!,
      model: process.env.EMBEDDING_MODEL!,
      dimensions: parseInt(process.env.EMBEDDING_DIMENSIONS || '384', 10),
      batchSize: parseInt(process.env.EMBEDDING_BATCH_SIZE || '100', 10),
    },
    pinecone: {
      apiKey: process.env.PINECONE_API_KEY || 'test-pinecone-key',
      indexName: process.env.PINECONE_INDEX_NAME || 'test-index',
      dimension: parseInt(process.env.PINECONE_DIMENSION || '384', 10),
    },
    skipExternalServices: false,
    mockExternalServices: mockExternal,
    testTimeout: parseInt(process.env.TEST_TIMEOUT || '30000', 10),
  };
}

/**
 * Checks if external services should be skipped in tests
 * @returns true if external services should be skipped
 */
export function shouldSkipExternalServices(): boolean {
  return process.env.SKIP_EXTERNAL_SERVICES === 'true';
}

/**
 * Checks if external services should be mocked in tests
 * @returns true if external services should be mocked
 */
export function shouldMockExternalServices(): boolean {
  return process.env.MOCK_EXTERNAL_SERVICES === 'true';
}

/**
 * Skips a test if external services are not available
 * @param testName Name of the test for logging
 */
export function skipIfExternalServicesUnavailable(testName: string): void {
  if (shouldSkipExternalServices()) {
    console.log(`Skipping ${testName} - external services disabled`);
    return;
  }

  try {
    getTestConfig();
  } catch (error) {
    console.log(`Skipping ${testName} - configuration missing: ${error instanceof Error ? error.message : String(error)}`);
    throw new Error('Test skipped due to missing configuration');
  }
}

/**
 * Creates a test description that indicates whether the test uses real or mocked services
 * @param baseDescription Base test description
 * @returns Enhanced test description
 */
export function createTestDescription(baseDescription: string): string {
  if (shouldSkipExternalServices()) {
    return `${baseDescription} (skipped - external services disabled)`;
  }
  if (shouldMockExternalServices()) {
    return `${baseDescription} (mocked services)`;
  }
  return `${baseDescription} (integration test)`;
}
