/**
 * Environment Variable Validation Service
 * 
 * This service provides strict validation of environment variables without fallback values.
 * It ensures that all required configuration is present and throws explicit errors when
 * required variables are missing, preventing silent failures in different environments.
 */

export interface EnvironmentConfig {
  // Application Configuration
  nodeEnv: string;
  port: number;
  host?: string;
  logLevel: string;

  // JWT Configuration
  jwtSecret: string;
  jwtExpiration: string;
  jwtRefreshExpiration: string;

  // LLM Configuration
  llmProvider: string;
  llmApiKey: string;
  llmBaseUrl: string;
  llmModel: string;
  llmTemperature: number;
  llmMaxTokens: number;
  llmTimeout: number;

  // Embedding Service Configuration
  embeddingProvider: string;
  embeddingApiKey: string;
  embeddingBaseUrl: string;
  embeddingModel: string;
  embeddingDimensions: number;
  embeddingBatchSize: number;

  // Pinecone Configuration
  pineconeApiKey: string;
  pineconeIndexName: string;
  pineconeDimension: number;

  // RAG Configuration
  ragEnabled: boolean;
  ragMaxResults: number;
  ragSimilarityThreshold: number;
  ragContextWindow: number;

  // MCP Server Configuration
  mcpPort: number;
  mcpServerUrl: string;

  // RAG System Configuration
  ragSystemUrl: string;

  // CORS Configuration
  corsOrigin: string;
}

export class EnvironmentValidationError extends Error {
  constructor(message: string, public missingVariables: string[] = []) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

/**
 * Required environment variables for different environments
 */
const REQUIRED_VARIABLES = {
  development: [
    'NODE_ENV',
    'PORT',
    'LOG_LEVEL',
    'JWT_SECRET',
    'JWT_EXPIRATION',
    'JWT_REFRESH_EXPIRATION',
    'LLM_PROVIDER',
    'LLM_API_KEY',
    'LLM_BASE_URL',
    'LLM_MODEL',
    'RAG_SYSTEM_URL',
    'CORS_ORIGIN',
  ],
  production: [
    'NODE_ENV',
    'PORT',
    'LOG_LEVEL',
    'JWT_SECRET',
    'JWT_EXPIRATION',
    'JWT_REFRESH_EXPIRATION',
    'LLM_PROVIDER',
    'LLM_API_KEY',
    'LLM_BASE_URL',
    'LLM_MODEL',
    'RAG_SYSTEM_URL',
    'CORS_ORIGIN',
  ],
  test: [
    'NODE_ENV',
    'JWT_SECRET',
    'LLM_PROVIDER',
    'RAG_SYSTEM_URL',
  ],
};

/**
 * Validates that all required environment variables are present
 * @param env Environment to validate for (development, production, test)
 * @throws EnvironmentValidationError if required variables are missing
 */
export function validateEnvironmentVariables(env: string = process.env.NODE_ENV || 'development'): void {
  const requiredVars = REQUIRED_VARIABLES[env as keyof typeof REQUIRED_VARIABLES] || REQUIRED_VARIABLES.development;
  const missingVars: string[] = [];

  for (const varName of requiredVars) {
    if (!process.env[varName] || process.env[varName]?.trim() === '') {
      missingVars.push(varName);
    }
  }

  if (missingVars.length > 0) {
    const message = `Missing required environment variables for ${env} environment:\n` +
      missingVars.map(v => `  - ${v}`).join('\n') + '\n\n' +
      'Please check your .env.local file and ensure all required variables are set.\n' +
      'See .env.example for reference.';
    
    throw new EnvironmentValidationError(message, missingVars);
  }
}

/**
 * Gets a required environment variable or throws an error
 * @param name Variable name
 * @param description Human-readable description for error messages
 * @returns The environment variable value
 * @throws EnvironmentValidationError if the variable is missing
 */
export function getRequiredEnvVar(name: string, description?: string): string {
  const value = process.env[name];
  if (!value || value.trim() === '') {
    const desc = description ? ` (${description})` : '';
    throw new EnvironmentValidationError(
      `Required environment variable ${name}${desc} is missing or empty.\n` +
      'Please set this variable in your .env.local file.'
    );
  }
  return value.trim();
}

/**
 * Gets a required environment variable as a number
 * @param name Variable name
 * @param description Human-readable description for error messages
 * @returns The environment variable value as a number
 * @throws EnvironmentValidationError if the variable is missing or not a valid number
 */
export function getRequiredEnvNumber(name: string, description?: string): number {
  const value = getRequiredEnvVar(name, description);
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    const desc = description ? ` (${description})` : '';
    throw new EnvironmentValidationError(
      `Environment variable ${name}${desc} must be a valid number, got: ${value}`
    );
  }
  return num;
}

/**
 * Gets a required environment variable as a boolean
 * @param name Variable name
 * @param description Human-readable description for error messages
 * @returns The environment variable value as a boolean
 */
export function getRequiredEnvBoolean(name: string, description?: string): boolean {
  const value = getRequiredEnvVar(name, description);
  return value.toLowerCase() === 'true';
}

/**
 * Gets a required environment variable as a float
 * @param name Variable name
 * @param description Human-readable description for error messages
 * @returns The environment variable value as a float
 * @throws EnvironmentValidationError if the variable is missing or not a valid number
 */
export function getRequiredEnvFloat(name: string, description?: string): number {
  const value = getRequiredEnvVar(name, description);
  const num = parseFloat(value);
  if (isNaN(num)) {
    const desc = description ? ` (${description})` : '';
    throw new EnvironmentValidationError(
      `Environment variable ${name}${desc} must be a valid number, got: ${value}`
    );
  }
  return num;
}

/**
 * Validates and returns the complete environment configuration
 * @returns Validated environment configuration
 * @throws EnvironmentValidationError if any required variables are missing or invalid
 */
export function getValidatedEnvironmentConfig(): EnvironmentConfig {
  // First validate that all required variables are present
  validateEnvironmentVariables();

  try {
    return {
      // Application Configuration
      nodeEnv: getRequiredEnvVar('NODE_ENV', 'Application environment'),
      port: getRequiredEnvNumber('PORT', 'Application port'),
      host: process.env.HOST, // Optional
      logLevel: getRequiredEnvVar('LOG_LEVEL', 'Logging level'),

      // JWT Configuration
      jwtSecret: getRequiredEnvVar('JWT_SECRET', 'JWT signing secret'),
      jwtExpiration: getRequiredEnvVar('JWT_EXPIRATION', 'JWT token expiration'),
      jwtRefreshExpiration: getRequiredEnvVar('JWT_REFRESH_EXPIRATION', 'JWT refresh token expiration'),

      // LLM Configuration
      llmProvider: getRequiredEnvVar('LLM_PROVIDER', 'LLM provider type'),
      llmApiKey: getRequiredEnvVar('LLM_API_KEY', 'LLM API key'),
      llmBaseUrl: getRequiredEnvVar('LLM_BASE_URL', 'LLM base URL'),
      llmModel: getRequiredEnvVar('LLM_MODEL', 'LLM model name'),
      llmTemperature: getRequiredEnvFloat('LLM_TEMPERATURE', 'LLM temperature'),
      llmMaxTokens: getRequiredEnvNumber('LLM_MAX_TOKENS', 'LLM max tokens'),
      llmTimeout: getRequiredEnvNumber('LLM_TIMEOUT', 'LLM request timeout'),

      // Embedding Service Configuration
      embeddingProvider: getRequiredEnvVar('EMBEDDING_PROVIDER', 'Embedding provider type'),
      embeddingApiKey: getRequiredEnvVar('EMBEDDING_API_KEY', 'Embedding API key'),
      embeddingBaseUrl: getRequiredEnvVar('EMBEDDING_BASE_URL', 'Embedding base URL'),
      embeddingModel: getRequiredEnvVar('EMBEDDING_MODEL', 'Embedding model name'),
      embeddingDimensions: getRequiredEnvNumber('EMBEDDING_DIMENSIONS', 'Embedding dimensions'),
      embeddingBatchSize: getRequiredEnvNumber('EMBEDDING_BATCH_SIZE', 'Embedding batch size'),

      // Pinecone Configuration
      pineconeApiKey: getRequiredEnvVar('PINECONE_API_KEY', 'Pinecone API key'),
      pineconeIndexName: getRequiredEnvVar('PINECONE_INDEX_NAME', 'Pinecone index name'),
      pineconeDimension: getRequiredEnvNumber('PINECONE_DIMENSION', 'Pinecone vector dimension'),

      // RAG Configuration
      ragEnabled: getRequiredEnvBoolean('RAG_ENABLED', 'RAG system enabled flag'),
      ragMaxResults: getRequiredEnvNumber('RAG_MAX_RESULTS', 'RAG max results'),
      ragSimilarityThreshold: getRequiredEnvFloat('RAG_SIMILARITY_THRESHOLD', 'RAG similarity threshold'),
      ragContextWindow: getRequiredEnvNumber('RAG_CONTEXT_WINDOW', 'RAG context window size'),

      // MCP Server Configuration
      mcpPort: getRequiredEnvNumber('MCP_PORT', 'MCP server port'),
      mcpServerUrl: getRequiredEnvVar('MCP_SERVER_URL', 'MCP server URL'),

      // CORS Configuration
      corsOrigin: getRequiredEnvVar('CORS_ORIGIN', 'CORS allowed origin'),
    };
  } catch (error) {
    if (error instanceof EnvironmentValidationError) {
      throw error;
    }
    throw new EnvironmentValidationError(
      `Failed to validate environment configuration: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
