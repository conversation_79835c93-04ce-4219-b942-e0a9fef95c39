# OTRS AI-Powered Project - Augment Rules Configuration
# This file defines project-specific coding standards, architecture patterns,
# and development practices for maintaining consistency across the codebase.

project:
  name: "OTRS AI-Powered"
  description: "AI-powered customer service platform with RAG, LLM integration, and microservices architecture"
  version: "1.0.0"
  
# Architecture Patterns
architecture:
  style: "microservices"
  patterns:
    - "NestJS backend with modular architecture"
    - "React frontend with TypeScript"
    - "Package-based monorepo structure"
    - "Dependency injection pattern for services"
    - "Interface segregation for service contracts"
    - "Factory pattern for LLM providers"
    - "Strategy pattern for different AI providers"
    - "Observer pattern for real-time updates"
    - "Command pattern for tool execution"
  
  principles:
    - "SOLID principles enforcement"
    - "DRY (Don't Repeat Yourself)"
    - "Early return pattern for cleaner code flow"
    - "Single responsibility functions"
    - "Human-readable, maintainable code"
    - "Clear naming conventions"

# Technology Stack
technology_stack:
  backend:
    framework: "NestJS"
    language: "TypeScript"
    runtime: "Node.js"
    database: "PostgreSQL"
    orm: "TypeORM"
    validation: "class-validator"
    testing: "Jest"
    
  frontend:
    framework: "React"
    language: "TypeScript"
    bundler: "Vite"
    styling: "CSS Modules / Styled Components"
    state_management: "React Context / Redux Toolkit"
    testing: "Jest + React Testing Library"
    e2e_testing: "Playwright"
    
  ai_ml:
    llm_providers:
      - "vLLM (primary)"
      - "OpenAI (fallback)"
      - "Google Gemini (alternative)"
    embedding_models:
      - "sentence-transformers/all-MiniLM-L6-v2 (384 dimensions)"
    vector_database: "Pinecone"
    rag_system: "Custom implementation"
    
  infrastructure:
    containerization: "Docker"
    orchestration: "Kubernetes"
    ci_cd: "GitHub Actions"
    monitoring: "Custom logging"
    
  communication:
    http_client: "Axios"
    websockets: "Socket.IO"
    mcp_protocol: "Model Context Protocol"

# File Organization and Naming Conventions
file_organization:
  structure: "packages-based monorepo"
  packages:
    - "backend: NestJS API server"
    - "frontend: React web application"
    - "shared: Common types and utilities"
    - "rag-system: RAG implementation package"
    - "mcp-server: Model Context Protocol server"
    - "model-tuning: ML model fine-tuning utilities"
    
  naming_conventions:
    files:
      - "kebab-case for file names"
      - ".service.ts for service classes"
      - ".controller.ts for API controllers"
      - ".interface.ts for TypeScript interfaces"
      - ".dto.ts for data transfer objects"
      - ".spec.ts for unit tests"
      - ".integration.spec.ts for integration tests"
      - ".e2e.spec.ts for end-to-end tests"
      
    directories:
      - "kebab-case for directory names"
      - "modules/ for feature modules"
      - "__tests__/ for test files"
      - "interfaces/ for type definitions"
      - "dto/ for data transfer objects"
      
    classes:
      - "PascalCase for class names"
      - "Service suffix for service classes"
      - "Controller suffix for controllers"
      - "Provider suffix for provider classes"
      - "Interface prefix for interfaces (IServiceName)"
      
    variables:
      - "camelCase for variables and functions"
      - "UPPER_SNAKE_CASE for constants"
      - "descriptive names over abbreviations"

# Environment Configuration Standards
environment_configuration:
  structure:
    - ".env.example: Template with placeholder values"
    - ".env.local: Development environment (gitignored)"
    - ".env.production: Production environment (gitignored)"
    - ".env.test: Test environment (gitignored)"
    - ".env: Default/fallback values (committed)"
    
  validation:
    - "Strict environment variable validation without fallbacks"
    - "Explicit error messages for missing variables"
    - "Startup validation that fails fast"
    - "Type-safe environment configuration"
    - "Required vs optional variable distinction"
    
  security:
    - "No hardcoded API keys in source code"
    - "Environment variables for all sensitive data"
    - "Placeholder values in example files"
    - "Real credentials only in gitignored files"
    - "Separate test credentials from production"

# Dependencies and Package Management
package_management:
  manager: "Yarn Workspaces"
  principles:
    - "Use package managers for dependency management"
    - "Never manually edit package.json for dependencies"
    - "Use workspace commands for monorepo operations"
    - "Pin exact versions for critical dependencies"
    - "Regular dependency updates with testing"
    
  commands:
    install: "yarn workspace <package-name> add <dependency>"
    remove: "yarn workspace <package-name> remove <dependency>"
    build: "yarn workspace <package-name> build"
    test: "yarn workspace <package-name> test"
    dev: "yarn dev (runs all packages)"

# Testing Approaches and Patterns
testing:
  strategy: "Test pyramid with comprehensive coverage"
  types:
    unit:
      - "Jest for unit testing"
      - "Mock external dependencies"
      - "Test business logic in isolation"
      - "High coverage for service classes"
      
    integration:
      - "Test service interactions"
      - "Real database connections in tests"
      - "External service integration testing"
      - "Environment-based test configuration"
      
    e2e:
      - "Playwright for browser automation"
      - "Complete user journey testing"
      - "RAG system functionality validation"
      - "Cross-browser compatibility testing"
      
  configuration:
    - "Package-specific test configurations"
    - "Environment-based test settings"
    - "Skip external services when unavailable"
    - "Mock vs real service configuration"
    - "Test data management and cleanup"

# Security Practices
security:
  api_keys:
    - "Environment variables only"
    - "No hardcoded credentials"
    - "Separate keys for different environments"
    - "Regular key rotation"
    - "Minimal permission principle"
    
  authentication:
    - "JWT-based authentication"
    - "Secure token storage"
    - "Token expiration and refresh"
    - "Role-based access control"
    
  data_protection:
    - "Input validation and sanitization"
    - "SQL injection prevention"
    - "XSS protection"
    - "CORS configuration"
    - "Rate limiting"

# Code Quality Standards
code_quality:
  linting:
    - "ESLint v9 for TypeScript packages"
    - "Package-specific ESLint configurations"
    - "Functional TypeScript linting preferences"
    - "Skip linting for Python packages"
    - "Consistent formatting with Prettier"
    
  documentation:
    - "JSDoc comments for public APIs"
    - "README files for each package"
    - "Architecture decision records"
    - "API documentation"
    - "Inline comments for complex logic"
    
  error_handling:
    - "Explicit error types and messages"
    - "Proper error propagation"
    - "Logging with appropriate levels"
    - "User-friendly error responses"
    - "Graceful degradation"

# AI/LLM Integration Patterns
ai_integration:
  llm_providers:
    - "Dependency injection pattern for providers"
    - "Factory pattern for provider selection"
    - "OpenAI-compatible API format preference"
    - "Fallback provider configuration"
    - "Provider-specific error handling"
    
  rag_system:
    - "Integrated package approach (not microservice)"
    - "Sentence-transformers for embeddings"
    - "Pinecone for vector storage"
    - "Query preprocessing and sanitization"
    - "Hybrid search capabilities"
    
  tool_calling:
    - "MCP (Model Context Protocol) integration"
    - "Simplified tool call flow"
    - "Direct LLM tool decision making"
    - "Tool execution with result feedback"
    - "Error handling for tool failures"

# Development Workflow
development:
  git:
    - "Feature branch workflow"
    - "Descriptive commit messages"
    - "Pull request reviews required"
    - "Automated testing on PRs"
    - "No direct commits to main"
    
  deployment:
    - "Docker containerization"
    - "Kubernetes orchestration"
    - "GitHub Actions CI/CD"
    - "Environment-specific deployments"
    - "Health checks and monitoring"
    
  debugging:
    - "Structured logging"
    - "Error tracking and monitoring"
    - "Performance profiling"
    - "Debug configurations for IDEs"
    - "Local development tools"

# Performance Considerations
performance:
  backend:
    - "Async/await for I/O operations"
    - "Connection pooling for databases"
    - "Caching strategies"
    - "Request/response optimization"
    - "Memory management"
    
  frontend:
    - "Code splitting and lazy loading"
    - "Component memoization"
    - "Bundle size optimization"
    - "Image optimization"
    - "Progressive loading"
    
  ai_services:
    - "Batch processing for embeddings"
    - "Connection reuse for LLM providers"
    - "Timeout configuration"
    - "Rate limiting compliance"
    - "Response streaming when available"
