import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  IRagService,
  FaqEntry,
  RelevantFaqResult,
  RagStatistics
} from './interfaces/rag.interface';

// Import from rag-system package
import {
  RagService as CoreRagService,
  RagConfig as CoreRagConfig
} from '@otrs-ai-powered/rag-system';

@Injectable()
export class RagService implements IRagService {
  private readonly logger = new Logger(RagService.name);
  private coreRagService: CoreRagService;
  private isInitialized = false;

  constructor(private readonly configService: ConfigService) {
    this.initializeCoreService();
  }

  /**
   * Initialize the core RAG service with configuration
   */
  private initializeCoreService(): void {
    const config: CoreRagConfig = {
      embedding: {
        provider: this.configService.get<string>('EMBEDDING_PROVIDER') as 'openai' | 'vllm' || 'vllm',
        apiKey: this.configService.get<string>('EMBEDDING_API_KEY') || 'test-key',
        model: this.configService.get<string>('EMBEDDING_MODEL') || 'sentence-transformers/all-MiniLM-L6-v2',
        baseUrl: this.configService.get<string>('EMBEDDING_BASE_URL'),
        dimensions: parseInt(this.configService.get<string>('EMBEDDING_DIMENSIONS') || '384'),
        batchSize: parseInt(this.configService.get<string>('EMBEDDING_BATCH_SIZE') || '100'),
        timeout: 30000,
      },
      vectorDb: {
        apiKey: this.configService.get<string>('PINECONE_API_KEY') || '',
        indexName: this.configService.get<string>('PINECONE_INDEX_NAME') || 'otrs-faq',
        dimension: parseInt(this.configService.get<string>('PINECONE_DIMENSION') || '384'),
        metric: 'cosine',
      },
      retrieval: {
        topK: parseInt(this.configService.get<string>('RAG_MAX_RESULTS') || '5'),
        similarityThreshold: parseFloat(this.configService.get<string>('RAG_SIMILARITY_THRESHOLD') || '0.7'),
        maxContextLength: parseInt(this.configService.get<string>('RAG_CONTEXT_WINDOW') || '4000'),
        enableReranking: this.configService.get<string>('RAG_ENABLE_RERANKING') === 'true',
      },
    };

    this.coreRagService = new CoreRagService(config);
  }

  /**
   * Initialize the RAG service with FAQ data
   */
  async initializeWithFaqData(faqData: FaqEntry[]): Promise<void> {
    try {
      this.logger.log('Initializing RAG service with FAQ data...');

      // Initialize the core service
      await this.coreRagService.initialize();

      // If FAQ data is provided, we could process and index it
      // For now, we'll use the Excel data from the rag-system package
      if (faqData && faqData.length > 0) {
        this.logger.log(`Processing ${faqData.length} FAQ entries`);
        // TODO: Convert backend FAQ entries to core service format and index
      } else {
        // Index the default Excel FAQ data
        await this.coreRagService.indexFaqData();
      }

      this.isInitialized = true;
      this.logger.log('RAG service initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize RAG service:', error);
      throw error;
    }
  }

  /**
   * Search for relevant FAQ entries based on user query
   */
  async searchRelevantFaqs(query: string, topK = 5): Promise<RelevantFaqResult[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      this.logger.debug(`Searching FAQs for query: "${query}"`);

      const searchResult = await this.coreRagService.searchFaqs(query, {
        topK,
        similarityThreshold: 0.6,
      });

      // Convert core service results to backend interface format
      const results: RelevantFaqResult[] = searchResult.results.map((result, index) => ({
        faqEntry: {
          id: result.id,
          question: result.question,
          answer: result.answer,
          category: result.category,
          metadata: result.metadata,
        },
        similarityScore: result.score,
        relevanceRank: index + 1,
      }));

      this.logger.debug(`Found ${results.length} relevant FAQs`);
      return results;

    } catch (error) {
      this.logger.error('Error searching FAQs:', error);
      throw error;
    }
  }

  /**
   * Add new FAQ entries to the vector database
   */
  async addFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      this.logger.log(`Adding ${faqEntries.length} FAQ entries`);
      // TODO: Implement adding new FAQ entries to the vector database
      // This would involve converting to the core service format and indexing
      this.logger.warn('addFaqEntries not yet implemented');
    } catch (error) {
      this.logger.error('Error adding FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Update existing FAQ entries in the vector database
   */
  async updateFaqEntries(faqEntries: FaqEntry[]): Promise<void> {
    try {
      this.logger.log(`Updating ${faqEntries.length} FAQ entries`);
      // TODO: Implement updating FAQ entries in the vector database
      this.logger.warn('updateFaqEntries not yet implemented');
    } catch (error) {
      this.logger.error('Error updating FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Delete FAQ entries from the vector database
   */
  async deleteFaqEntries(faqIds: string[]): Promise<void> {
    try {
      this.logger.log(`Deleting ${faqIds.length} FAQ entries`);
      // TODO: Implement deleting FAQ entries from the vector database
      this.logger.warn('deleteFaqEntries not yet implemented');
    } catch (error) {
      this.logger.error('Error deleting FAQ entries:', error);
      throw error;
    }
  }

  /**
   * Check if the RAG service is available and ready
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (!this.coreRagService) {
        return false;
      }

      return await this.coreRagService.isAvailable();
    } catch (error) {
      this.logger.warn('RAG service availability check failed:', error);
      return false;
    }
  }

  /**
   * Get statistics about the RAG system
   */
  async getStatistics(): Promise<RagStatistics> {
    try {
      const coreStats = await this.coreRagService.getStatistics();
      
      return {
        totalFaqEntries: coreStats.vectorDatabase?.totalVectors || 0,
        vectorDimensions: coreStats.embedding?.dimensions || 1536,
        lastUpdated: new Date().toISOString(),
        indexHealth: coreStats.isInitialized ? 'healthy' : 'unhealthy',
      };

    } catch (error) {
      this.logger.error('Error getting RAG statistics:', error);
      return {
        totalFaqEntries: 0,
        vectorDimensions: 1536,
        lastUpdated: new Date().toISOString(),
        indexHealth: 'unhealthy',
      };
    }
  }

  /**
   * Get available FAQ categories
   */
  async getCategories(): Promise<string[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      return await this.coreRagService.getCategories();
    } catch (error) {
      this.logger.error('Error getting categories:', error);
      return [];
    }
  }

  /**
   * Perform hybrid search with filters
   */
  async hybridSearch(
    query: string, 
    filters: Record<string, any> = {}, 
    topK = 5
  ): Promise<RelevantFaqResult[]> {
    try {
      if (!this.isInitialized) {
        await this.initializeWithFaqData([]);
      }

      const searchResult = await this.coreRagService.hybridSearch(query, filters, topK);

      return searchResult.results.map((result, index) => ({
        faqEntry: {
          id: result.id,
          question: result.question,
          answer: result.answer,
          category: result.category,
          metadata: result.metadata,
        },
        similarityScore: result.score,
        relevanceRank: index + 1,
      }));

    } catch (error) {
      this.logger.error('Error in hybrid search:', error);
      throw error;
    }
  }

  /**
   * Initialize the service on module startup
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('RAG service module initializing...');
      
      // Check if we should auto-initialize
      const autoInit = this.configService.get<string>('RAG_AUTO_INIT') === 'true';
      if (autoInit) {
        await this.initializeWithFaqData([]);
      }

    } catch (error) {
      this.logger.warn('RAG service auto-initialization failed:', error);
    }
  }
}
