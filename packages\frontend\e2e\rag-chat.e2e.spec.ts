/**
 * End-to-End RAG Chat Tests
 * 
 * Playwright-based tests that simulate real user interactions with the chat UI
 * and verify RAG system functionality from an end-user perspective.
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.E2E_BASE_URL || 'http://localhost:3000',
  apiUrl: process.env.E2E_API_URL || 'http://localhost:4000',
  timeout: 30000,
  skipExternalServices: process.env.SKIP_EXTERNAL_SERVICES === 'true',
};

// Test data
const TEST_QUERIES = {
  ragTrigger: 'How do I reset my password?',
  generalQuestion: 'What is OTRS?',
  complexQuery: 'I need help with creating a new ticket and setting up email notifications',
  irrelevantQuery: 'What is the weather like today?',
  emptyQuery: '',
  longQuery: 'I have a very complex issue with my account where I cannot log in and I have tried multiple times to reset my password but the email is not coming through and I need urgent help because this is affecting my work and I cannot access any of my tickets or customer information which is critical for my daily operations',
};

// Helper functions
async function navigateToChat(page: Page): Promise<void> {
  await page.goto(TEST_CONFIG.baseUrl);
  await page.waitForLoadState('networkidle');
  
  // Look for chat interface elements
  const chatContainer = page.locator('[data-testid="chat-container"], .chat-container, #chat');
  await expect(chatContainer).toBeVisible({ timeout: 10000 });
}

async function sendMessage(page: Page, message: string): Promise<void> {
  const messageInput = page.locator('[data-testid="message-input"], input[type="text"], textarea');
  const sendButton = page.locator('[data-testid="send-button"], button[type="submit"], .send-btn');
  
  await messageInput.fill(message);
  await sendButton.click();
}

async function waitForResponse(page: Page): Promise<string> {
  // Wait for the response to appear
  const responseSelector = '[data-testid="ai-response"], .ai-message, .response-message';
  await page.waitForSelector(responseSelector, { timeout: TEST_CONFIG.timeout });
  
  const response = await page.locator(responseSelector).last().textContent();
  return response || '';
}

async function checkRagIndicator(page: Page): Promise<boolean> {
  // Look for RAG indicators in the UI
  const ragIndicators = [
    '[data-testid="rag-indicator"]',
    '.rag-enhanced',
    '.faq-based',
    '[title*="FAQ"]',
    '[title*="knowledge base"]'
  ];
  
  for (const selector of ragIndicators) {
    const element = page.locator(selector);
    if (await element.isVisible()) {
      return true;
    }
  }
  
  return false;
}

test.describe('RAG Chat End-to-End Tests', () => {
  test.beforeEach(async ({ page }) => {
    if (TEST_CONFIG.skipExternalServices) {
      test.skip('Skipping E2E tests - external services disabled');
    }
    
    // Set up page with proper viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Navigate to chat interface
    await navigateToChat(page);
  });

  test.describe('Basic Chat Functionality', () => {
    test('should load chat interface successfully', async ({ page }) => {
      // Verify chat interface elements are present
      await expect(page.locator('[data-testid="chat-container"], .chat-container')).toBeVisible();
      await expect(page.locator('[data-testid="message-input"], input, textarea')).toBeVisible();
      await expect(page.locator('[data-testid="send-button"], button[type="submit"]')).toBeVisible();
    });

    test('should send and receive messages', async ({ page }) => {
      const testMessage = 'Hello, this is a test message';
      
      await sendMessage(page, testMessage);
      
      // Verify message appears in chat
      await expect(page.locator('text=' + testMessage)).toBeVisible();
      
      // Wait for and verify response
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
    });

    test('should handle empty messages gracefully', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.emptyQuery);
      
      // Should either prevent sending or handle gracefully
      const errorMessage = page.locator('[data-testid="error-message"], .error, .warning');
      const isErrorVisible = await errorMessage.isVisible();
      
      if (!isErrorVisible) {
        // If no error, should still get a response
        const response = await waitForResponse(page);
        expect(response.length).toBeGreaterThan(0);
      }
    });
  });

  test.describe('RAG System Integration', () => {
    test('should trigger RAG for FAQ-related queries', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.ragTrigger);
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
      
      // Check for RAG indicators or FAQ-specific content
      const hasRagIndicator = await checkRagIndicator(page);
      const responseContainsFaqContent = response.toLowerCase().includes('password') || 
                                       response.toLowerCase().includes('reset') ||
                                       response.toLowerCase().includes('login');
      
      expect(hasRagIndicator || responseContainsFaqContent).toBe(true);
    });

    test('should handle complex multi-part queries', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.complexQuery);
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
      
      // Response should address multiple aspects of the query
      const responseText = response.toLowerCase();
      const addressesTickets = responseText.includes('ticket');
      const addressesEmail = responseText.includes('email') || responseText.includes('notification');
      
      expect(addressesTickets || addressesEmail).toBe(true);
    });

    test('should fallback gracefully for irrelevant queries', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.irrelevantQuery);
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
      
      // Should provide helpful fallback response
      const responseText = response.toLowerCase();
      const isHelpfulFallback = responseText.includes('help') || 
                               responseText.includes('assist') ||
                               responseText.includes('support') ||
                               responseText.includes('contact');
      
      expect(isHelpfulFallback).toBe(true);
    });

    test('should handle very long queries', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.longQuery);
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
      
      // Should process and respond to long queries
      const responseText = response.toLowerCase();
      const addressesMainConcerns = responseText.includes('password') || 
                                   responseText.includes('login') ||
                                   responseText.includes('account') ||
                                   responseText.includes('ticket');
      
      expect(addressesMainConcerns).toBe(true);
    });
  });

  test.describe('User Experience and Interface', () => {
    test('should show typing indicators during processing', async ({ page }) => {
      await sendMessage(page, TEST_QUERIES.ragTrigger);
      
      // Look for typing indicators
      const typingIndicators = [
        '[data-testid="typing-indicator"]',
        '.typing',
        '.loading',
        '.processing'
      ];
      
      let foundTypingIndicator = false;
      for (const selector of typingIndicators) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          foundTypingIndicator = true;
          break;
        }
      }
      
      // Wait for response to complete
      await waitForResponse(page);
      
      // Typing indicator should disappear
      for (const selector of typingIndicators) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          await expect(element).not.toBeVisible();
        }
      }
    });

    test('should maintain chat history', async ({ page }) => {
      const firstMessage = 'First test message';
      const secondMessage = 'Second test message';
      
      // Send first message
      await sendMessage(page, firstMessage);
      await waitForResponse(page);
      
      // Send second message
      await sendMessage(page, secondMessage);
      await waitForResponse(page);
      
      // Verify both messages are still visible
      await expect(page.locator('text=' + firstMessage)).toBeVisible();
      await expect(page.locator('text=' + secondMessage)).toBeVisible();
    });

    test('should handle rapid message sending', async ({ page }) => {
      const messages = ['Message 1', 'Message 2', 'Message 3'];
      
      // Send messages rapidly
      for (const message of messages) {
        await sendMessage(page, message);
        // Small delay to avoid overwhelming the system
        await page.waitForTimeout(500);
      }
      
      // Wait for all responses
      await page.waitForTimeout(5000);
      
      // Verify all messages are visible
      for (const message of messages) {
        await expect(page.locator('text=' + message)).toBeVisible();
      }
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      await sendMessage(page, 'Test message during network failure');
      
      // Should show error message or retry mechanism
      const errorElements = [
        '[data-testid="error-message"]',
        '.error',
        '.network-error',
        'text=Error',
        'text=Failed',
        'text=Retry'
      ];
      
      let foundError = false;
      for (const selector of errorElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          foundError = true;
          break;
        }
      }
      
      expect(foundError).toBe(true);
    });

    test('should handle special characters in messages', async ({ page }) => {
      const specialMessage = '!@#$%^&*()_+{}|:"<>?[]\\;\',./ Special chars test';
      
      await sendMessage(page, specialMessage);
      
      // Should handle special characters without breaking
      await expect(page.locator('text=' + specialMessage)).toBeVisible();
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
    });

    test('should handle session timeout', async ({ page }) => {
      // Simulate long idle time
      await page.waitForTimeout(2000);
      
      await sendMessage(page, 'Message after idle time');
      
      // Should either maintain session or handle re-authentication
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
    });
  });

  test.describe('Accessibility and Responsive Design', () => {
    test('should be keyboard accessible', async ({ page }) => {
      // Navigate using keyboard
      await page.keyboard.press('Tab');
      await page.keyboard.type('Keyboard navigation test');
      await page.keyboard.press('Enter');
      
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
    });

    test('should work on mobile viewport', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Verify chat interface adapts to mobile
      await expect(page.locator('[data-testid="chat-container"], .chat-container')).toBeVisible();
      
      await sendMessage(page, 'Mobile test message');
      const response = await waitForResponse(page);
      expect(response.length).toBeGreaterThan(0);
    });

    test('should have proper ARIA labels', async ({ page }) => {
      // Check for accessibility attributes
      const messageInput = page.locator('[data-testid="message-input"], input, textarea');
      const sendButton = page.locator('[data-testid="send-button"], button[type="submit"]');
      
      // Verify ARIA labels or accessible names exist
      const inputAccessibleName = await messageInput.getAttribute('aria-label') || 
                                  await messageInput.getAttribute('placeholder');
      const buttonAccessibleName = await sendButton.getAttribute('aria-label') || 
                                   await sendButton.textContent();
      
      expect(inputAccessibleName).toBeTruthy();
      expect(buttonAccessibleName).toBeTruthy();
    });
  });
});
